# 获客系统设计文档

## 1. 项目概述

基于Django框架和MySQL数据库的获客管理系统，支持三级权限管理（主账号、组长账号、员工账号），实现客户数据的分配、跟进和统计功能。

## 2. 技术架构

- **后端框架**: Django 4.x
- **数据库**: MySQL 8.0+
- **认证系统**: Django内置Auth系统
- **管理后台**: Django Admin
- **前端**: Django模板 + Bootstrap
- **文件处理**: pandas (Excel导入)

## 3. 数据库设计

### 3.1 用户扩展表 (UserProfile)

```python
class UserProfile(models.Model):
    ROLE_CHOICES = [
        ('admin', '主账号'),
        ('leader', '组长账号'),
        ('employee', '员工账号'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户')
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, verbose_name='角色')
    leader_id = models.IntegerField(null=True, blank=True, verbose_name='所属组长ID')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    
    class Meta:
        verbose_name = '用户资料'
        verbose_name_plural = '用户资料'
```

### 3.2 客户数据表 (Customer)

```python
class Customer(models.Model):
    STATUS_CHOICES = [
        ('not_connected', '未接通'),
        ('inviting', '邀约中'),
        ('interested', '意向客户'),
        ('to_sign', '待签约'),
        ('loan_success', '放款成功'),
    ]
    
    name = models.CharField(max_length=100, verbose_name='姓名')
    phone = models.CharField(max_length=20, verbose_name='电话')
    age = models.IntegerField(verbose_name='年龄')
    address = models.TextField(verbose_name='地址')
    import_date = models.DateTimeField(verbose_name='导入日期')
    receive_date = models.DateTimeField(null=True, blank=True, verbose_name='领取日期')
    end_date = models.DateTimeField(null=True, blank=True, verbose_name='结束日期')
    follower_id = models.IntegerField(null=True, blank=True, verbose_name='跟进人ID')
    assigner_id = models.IntegerField(null=True, blank=True, verbose_name='分配人ID')
    remover_id = models.IntegerField(null=True, blank=True, verbose_name='移除人ID')
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='金额')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='not_connected', verbose_name='状态')
    follow_records = models.TextField(blank=True, verbose_name='跟进记录')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '客户数据'
        verbose_name_plural = '客户数据'
```

### 3.3 客户数据历史表 (CustomerHistory)

```python
class CustomerHistory(models.Model):
    customer_id = models.IntegerField(verbose_name='客户ID')
    employee_id = models.IntegerField(verbose_name='员工ID')
    start_date = models.DateTimeField(verbose_name='开始拥有时间')
    end_date = models.DateTimeField(null=True, blank=True, verbose_name='结束拥有时间')
    operation_type = models.CharField(max_length=20, verbose_name='操作类型')  # assign/remove
    operator_id = models.IntegerField(verbose_name='操作人ID')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '客户历史记录'
        verbose_name_plural = '客户历史记录'
```

### 3.4 开单记录表 (OrderRecord)

```python
class OrderRecord(models.Model):
    customer_id = models.IntegerField(verbose_name='客户ID')
    employee_id = models.IntegerField(verbose_name='员工ID')
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='放款金额')
    loan_date = models.DateTimeField(verbose_name='放款日期')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='记录创建时间')
    
    class Meta:
        verbose_name = '开单记录'
        verbose_name_plural = '开单记录'
```

## 4. 权限设计

### 4.1 Django Groups权限组

```python
# 在数据迁移中创建权限组
def create_groups():
    # 主账号组
    admin_group, created = Group.objects.get_or_create(name='admin_group')
    admin_permissions = [
        'add_customer', 'change_customer', 'delete_customer', 'view_customer',
        'add_userprofile', 'change_userprofile', 'delete_userprofile', 'view_userprofile',
        'view_customerhistory', 'view_orderrecord'
    ]
    
    # 组长组
    leader_group, created = Group.objects.get_or_create(name='leader_group')
    leader_permissions = [
        'change_customer', 'view_customer',
        'add_userprofile', 'view_userprofile',
        'view_customerhistory', 'view_orderrecord'
    ]
    
    # 员工组
    employee_group, created = Group.objects.get_or_create(name='employee_group')
    employee_permissions = [
        'change_customer', 'view_customer',
        'view_orderrecord'
    ]
```

### 4.2 自定义权限装饰器

```python
from functools import wraps
from django.core.exceptions import PermissionDenied

def role_required(allowed_roles):
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if not hasattr(request.user, 'userprofile'):
                raise PermissionDenied
            if request.user.userprofile.role not in allowed_roles:
                raise PermissionDenied
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator
```

## 5. 核心功能模块

### 5.1 用户管理模块

- **用户注册**: 仅主账号可创建组长和员工账号
- **用户认证**: 使用Django内置认证系统
- **权限控制**: 基于角色的权限控制

### 5.2 客户管理模块

- **数据导入**: Excel文件导入客户基础信息
- **客户分配**: 主账号分配给组长/员工，组长分配给员工
- **客户跟进**: 员工更新客户状态和跟进记录
- **客户移除**: 移除到公海池

### 5.3 统计查询模块

- **客户数量统计**: 按时间段统计拥有客户数量
- **业绩统计**: 统计放款成功客户数量和金额

## 6. Django Admin配置

### 6.1 用户管理Admin

```python
@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'role', 'leader_id', 'is_active', 'created_at']
    list_filter = ['role', 'is_active']
    search_fields = ['user__username', 'user__first_name']
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if request.user.userprofile.role == 'leader':
            # 组长只能看到自己的员工
            return qs.filter(leader_id=request.user.id)
        elif request.user.userprofile.role == 'employee':
            # 员工只能看到自己
            return qs.filter(user=request.user)
        return qs
```

### 6.2 客户管理Admin

```python
@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ['name', 'phone', 'status', 'follower_id', 'amount', 'receive_date']
    list_filter = ['status', 'import_date', 'receive_date']
    search_fields = ['name', 'phone']
    actions = ['assign_customers', 'remove_to_pool']
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if request.user.userprofile.role == 'employee':
            # 员工只能看到自己的客户
            return qs.filter(follower_id=request.user.id)
        elif request.user.userprofile.role == 'leader':
            # 组长看到自己和下属员工的客户
            employee_ids = UserProfile.objects.filter(
                leader_id=request.user.id
            ).values_list('user_id', flat=True)
            return qs.filter(follower_id__in=list(employee_ids) + [request.user.id])
        return qs
```

## 7. 核心视图设计

### 7.1 客户导入视图

```python
@role_required(['admin'])
def import_customers(request):
    if request.method == 'POST':
        excel_file = request.FILES['excel_file']
        df = pd.read_excel(excel_file)
        
        for _, row in df.iterrows():
            Customer.objects.create(
                name=row['姓名'],
                phone=row['电话'],
                age=row['年龄'],
                address=row['地址'],
                import_date=timezone.now()
            )
        
        messages.success(request, f'成功导入{len(df)}条客户数据')
        return redirect('customer_list')
    
    return render(request, 'import_customers.html')
```

### 7.2 客户分配视图

```python
@role_required(['admin', 'leader'])
def assign_customers(request):
    if request.method == 'POST':
        customer_ids = request.POST.getlist('customer_ids')
        employee_id = request.POST.get('employee_id')
        
        customers = Customer.objects.filter(id__in=customer_ids)
        for customer in customers:
            # 更新客户信息
            customer.follower_id = employee_id
            customer.assigner_id = request.user.id
            customer.receive_date = timezone.now()
            customer.save()
            
            # 记录历史
            CustomerHistory.objects.create(
                customer_id=customer.id,
                employee_id=employee_id,
                start_date=timezone.now(),
                operation_type='assign',
                operator_id=request.user.id
            )
        
        messages.success(request, f'成功分配{len(customers)}个客户')
        return redirect('customer_list')
    
    return render(request, 'assign_customers.html')
```

## 8. 部署配置

### 8.1 数据库配置

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'bgmanage_db',
        'USER': 'your_username',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
        },
    }
}
```

### 8.2 必要的Django设置

```python
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'customers',  # 客户管理应用
]

# 中文本地化
LANGUAGE_CODE = 'zh-hans'
TIME_ZONE = 'Asia/Shanghai'
USE_I18N = True
USE_TZ = True
```

## 9. 开发计划

1. **环境搭建**: Django项目初始化，MySQL数据库配置
2. **模型设计**: 创建数据模型和迁移文件
3. **权限系统**: 配置用户角色和权限
4. **Admin配置**: 自定义Django Admin界面
5. **核心功能**: 实现客户管理、分配、统计功能
6. **前端界面**: 开发用户友好的操作界面
7. **测试部署**: 功能测试和生产环境部署

这个设计充分利用了Django的内置功能，减少了代码量，同时满足了所有业务需求。
