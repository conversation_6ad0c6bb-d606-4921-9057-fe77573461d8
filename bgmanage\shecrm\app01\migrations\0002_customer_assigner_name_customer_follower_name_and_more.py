# Generated by Django 4.2.23 on 2025-07-02 07:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app01', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='customer',
            name='assigner_name',
            field=models.CharField(blank=True, max_length=100, verbose_name='分配人姓名'),
        ),
        migrations.AddField(
            model_name='customer',
            name='follower_name',
            field=models.CharField(blank=True, max_length=100, verbose_name='跟进人姓名'),
        ),
        migrations.AddField(
            model_name='customer',
            name='remover_name',
            field=models.CharField(blank=True, max_length=100, verbose_name='移除人姓名'),
        ),
        migrations.AddField(
            model_name='customerhistory',
            name='assigner_name',
            field=models.CharField(blank=True, max_length=100, verbose_name='分配人姓名'),
        ),
        migrations.AddField(
            model_name='customerhistory',
            name='follower_name',
            field=models.Char<PERSON>ield(blank=True, max_length=100, verbose_name='跟进人姓名'),
        ),
        migrations.AddField(
            model_name='customerhistory',
            name='remover_name',
            field=models.CharField(blank=True, max_length=100, verbose_name='移除人姓名'),
        ),
    ]
