#!/usr/bin/env python
"""
创建测试用户（没有UserProfile）供组长测试添加员工功能
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shecrm.settings')
django.setup()

from django.contrib.auth.models import User

def create_test_users():
    """创建几个测试用户（没有UserProfile）"""
    test_users = [
        {
            'username': 'newuser01',
            'first_name': '王',
            'last_name': '新员工',
            'email': '<EMAIL>',
            'password': '123456'
        },
        {
            'username': 'newuser02', 
            'first_name': '李',
            'last_name': '新员工',
            'email': '<EMAIL>',
            'password': '123456'
        }
    ]
    
    for user_data in test_users:
        user, created = User.objects.get_or_create(
            username=user_data['username'],
            defaults={
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name'],
                'email': user_data['email'],
                'is_staff': True,  # 必须是staff才能登录admin
                'is_active': True
            }
        )
        if created:
            user.set_password(user_data['password'])
            user.save()
            print(f"创建测试用户: {user.username}")
        else:
            print(f"用户 {user.username} 已存在")

def main():
    print("创建测试用户...")
    create_test_users()
    print("完成！现在组长可以为这些用户创建员工资料了。")

if __name__ == '__main__':
    main()
