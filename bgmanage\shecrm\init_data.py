#!/usr/bin/env python
"""
初始化数据脚本
为现有的超级用户创建UserProfile，并创建一些测试数据
"""

import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shecrm.settings')
django.setup()

from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from app01.models import UserProfile, Customer, CustomerHistory, OrderRecord
from django.utils import timezone

def create_groups_and_permissions():
    """创建权限组"""
    print("创建权限组...")
    
    # 获取内容类型
    customer_ct = ContentType.objects.get_for_model(Customer)
    userprofile_ct = ContentType.objects.get_for_model(UserProfile)
    customerhistory_ct = ContentType.objects.get_for_model(CustomerHistory)
    orderrecord_ct = ContentType.objects.get_for_model(OrderRecord)
    
    # 主账号组
    admin_group, created = Group.objects.get_or_create(name='admin_group')
    if created:
        print("创建主账号权限组")
        admin_permissions = Permission.objects.filter(
            content_type__in=[customer_ct, userprofile_ct, customerhistory_ct, orderrecord_ct]
        )
        admin_group.permissions.set(admin_permissions)
    
    # 组长组
    leader_group, created = Group.objects.get_or_create(name='leader_group')
    if created:
        print("创建组长权限组")
        leader_permissions = Permission.objects.filter(
            content_type__in=[customer_ct, userprofile_ct, customerhistory_ct, orderrecord_ct],
            codename__in=['change_customer', 'view_customer', 'add_userprofile', 'view_userprofile', 
                         'view_customerhistory', 'view_orderrecord']
        )
        leader_group.permissions.set(leader_permissions)
    
    # 员工组
    employee_group, created = Group.objects.get_or_create(name='employee_group')
    if created:
        print("创建员工权限组")
        employee_permissions = Permission.objects.filter(
            content_type__in=[customer_ct, orderrecord_ct],
            codename__in=['change_customer', 'view_customer', 'view_orderrecord']
        )
        employee_group.permissions.set(employee_permissions)

def create_user_profiles():
    """为现有用户创建UserProfile"""
    print("创建用户资料...")
    
    # 为超级用户创建主账号资料
    try:
        admin_user = User.objects.get(username='crmadmin')
        profile, created = UserProfile.objects.get_or_create(
            user=admin_user,
            defaults={
                'role': 'admin',
                'leader_id': None,
                'is_active': True
            }
        )
        if created:
            print(f"为用户 {admin_user.username} 创建主账号资料")
            # 添加到主账号组
            admin_group = Group.objects.get(name='admin_group')
            admin_user.groups.add(admin_group)
        else:
            print(f"用户 {admin_user.username} 的资料已存在")
    except User.DoesNotExist:
        print("未找到crmadmin用户，请先创建超级用户")

def create_test_data():
    """创建测试数据"""
    print("创建测试数据...")
    
    # 创建测试组长用户
    leader_user, created = User.objects.get_or_create(
        username='leader01',
        defaults={
            'first_name': '张',
            'last_name': '组长',
            'email': '<EMAIL>',
            'is_staff': True
        }
    )
    if created:
        leader_user.set_password('123456')
        leader_user.save()
        print("创建测试组长用户: leader01")
    
    # 为组长创建资料
    leader_profile, created = UserProfile.objects.get_or_create(
        user=leader_user,
        defaults={
            'role': 'leader',
            'leader_id': None,
            'is_active': True
        }
    )
    if created:
        leader_group = Group.objects.get(name='leader_group')
        leader_user.groups.add(leader_group)
        print("为组长创建用户资料")
    
    # 创建测试员工用户
    employee_user, created = User.objects.get_or_create(
        username='employee01',
        defaults={
            'first_name': '李',
            'last_name': '员工',
            'email': '<EMAIL>',
            'is_staff': True
        }
    )
    if created:
        employee_user.set_password('123456')
        employee_user.save()
        print("创建测试员工用户: employee01")
    
    # 为员工创建资料
    employee_profile, created = UserProfile.objects.get_or_create(
        user=employee_user,
        defaults={
            'role': 'employee',
            'leader_id': leader_user.id,
            'is_active': True
        }
    )
    if created:
        employee_group = Group.objects.get(name='employee_group')
        employee_user.groups.add(employee_group)
        print("为员工创建用户资料")
    
    # 创建测试客户数据
    if not Customer.objects.exists():
        customers_data = [
            {'name': '王小明', 'phone': '13800138001', 'age': 30, 'address': '北京市朝阳区'},
            {'name': '李小红', 'phone': '13800138002', 'age': 25, 'address': '上海市浦东新区'},
            {'name': '张小强', 'phone': '13800138003', 'age': 35, 'address': '广州市天河区'},
            {'name': '赵小美', 'phone': '13800138004', 'age': 28, 'address': '深圳市南山区'},
            {'name': '刘小刚', 'phone': '13800138005', 'age': 32, 'address': '杭州市西湖区'},
        ]
        
        for data in customers_data:
            Customer.objects.create(
                name=data['name'],
                phone=data['phone'],
                age=data['age'],
                address=data['address'],
                import_date=timezone.now(),
                status='not_connected'
            )
        print(f"创建了 {len(customers_data)} 个测试客户")

def main():
    print("开始初始化数据...")
    create_groups_and_permissions()
    create_user_profiles()
    create_test_data()
    print("数据初始化完成！")
    print("\n测试账号信息：")
    print("主账号: crmadmin / 11crm123")
    print("组长账号: leader01 / 123456")
    print("员工账号: employee01 / 123456")

if __name__ == '__main__':
    main()
