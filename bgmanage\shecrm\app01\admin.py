from django.contrib import admin
from django.contrib.auth.models import User
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import UserProfile, Customer, CustomerHistory, OrderRecord

# Register your models here.

# 内联用户资料
class UserProfileInline(admin.StackedInline):
    model = UserProfile
    can_delete = False
    verbose_name_plural = '用户资料'

# 扩展用户管理
class UserAdmin(BaseUserAdmin):
    inlines = (UserProfileInline,)

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if hasattr(request.user, 'userprofile'):
            if request.user.userprofile.role == 'leader':
                # 组长能看到自己和名下的员工
                employee_ids = UserProfile.objects.filter(
                    leader_id=request.user.id
                ).values_list('user_id', flat=True)
                return qs.filter(id__in=list(employee_ids) + [request.user.id])
            elif request.user.userprofile.role == 'employee':
                # 员工只能看到自己
                return qs.filter(id=request.user.id)
        return qs

    def has_add_permission(self, request):
        # 主账号和组长可以添加用户
        if hasattr(request.user, 'userprofile'):
            return request.user.userprofile.role in ['admin', 'leader']
        return super().has_add_permission(request)

    def has_change_permission(self, request, obj=None):
        if hasattr(request.user, 'userprofile'):
            if request.user.userprofile.role == 'admin':
                return True
            elif request.user.userprofile.role == 'leader':
                if obj is None:
                    return True
                # 组长可以修改自己和名下员工
                employee_ids = UserProfile.objects.filter(
                    leader_id=request.user.id
                ).values_list('user_id', flat=True)
                return obj.id in list(employee_ids) + [request.user.id]
            elif request.user.userprofile.role == 'employee':
                return obj and obj.id == request.user.id
        return super().has_change_permission(request, obj)

    def has_delete_permission(self, request, obj=None):
        if hasattr(request.user, 'userprofile'):
            if request.user.userprofile.role == 'admin':
                return True
            elif request.user.userprofile.role == 'leader' and obj:
                # 组长可以删除名下员工，但不能删除自己
                if obj.id == request.user.id:
                    return False
                employee_ids = UserProfile.objects.filter(
                    leader_id=request.user.id
                ).values_list('user_id', flat=True)
                return obj.id in employee_ids
        return False

# 重新注册User模型
admin.site.unregister(User)
admin.site.register(User, UserAdmin)


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'role', 'leader_id', 'is_active', 'created_at']
    list_filter = ['role', 'is_active', 'created_at']
    search_fields = ['user__username', 'user__first_name', 'user__last_name']
    readonly_fields = ['created_at']

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if hasattr(request.user, 'userprofile'):
            if request.user.userprofile.role == 'leader':
                # 组长只能看到自己的员工
                return qs.filter(leader_id=request.user.id)
            elif request.user.userprofile.role == 'employee':
                # 员工只能看到自己
                return qs.filter(user=request.user)
        return qs

    def has_add_permission(self, request):
        # 只有主账号和组长可以添加用户
        if hasattr(request.user, 'userprofile'):
            return request.user.userprofile.role in ['admin', 'leader']
        return False

    def has_change_permission(self, request, obj=None):
        if hasattr(request.user, 'userprofile'):
            if request.user.userprofile.role == 'admin':
                return True
            elif request.user.userprofile.role == 'leader':
                if obj is None:
                    return True
                # 组长可以修改自己和名下员工的资料
                return obj.leader_id == request.user.id or obj.user == request.user
            elif request.user.userprofile.role == 'employee' and obj:
                return obj.user == request.user
        return False

    def has_delete_permission(self, request, obj=None):
        if hasattr(request.user, 'userprofile'):
            if request.user.userprofile.role == 'admin':
                return True
            elif request.user.userprofile.role == 'leader' and obj:
                # 组长可以删除名下员工，但不能删除自己
                return obj.leader_id == request.user.id and obj.user != request.user
        return False


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ['name', 'phone', 'status', 'follower_name', 'assigner_name', 'amount', 'receive_date', 'import_date']
    list_filter = ['status', 'import_date', 'receive_date']
    search_fields = ['name', 'phone']
    readonly_fields = ['created_at', 'updated_at']
    actions = ['assign_customers', 'remove_to_pool']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'phone', 'age', 'address')
        }),
        ('业务信息', {
            'fields': ('status', 'amount', ('follower_id', 'follower_name'), ('assigner_id', 'assigner_name'), ('remover_id', 'remover_name'))
        }),
        ('时间信息', {
            'fields': ('import_date', 'receive_date', 'end_date', 'created_at', 'updated_at')
        }),
        ('跟进记录', {
            'fields': ('follow_records',),
            'classes': ('wide',)
        }),
    )

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if hasattr(request.user, 'userprofile'):
            if request.user.userprofile.role == 'employee':
                # 员工只能看到自己的客户
                return qs.filter(follower_id=request.user.id)
            elif request.user.userprofile.role == 'leader':
                # 组长看到自己和下属员工的客户
                employee_ids = UserProfile.objects.filter(
                    leader_id=request.user.id
                ).values_list('user_id', flat=True)
                return qs.filter(follower_id__in=list(employee_ids) + [request.user.id])
        return qs

    def has_add_permission(self, request):
        # 所有角色都可以添加客户
        return True

    def has_change_permission(self, request, obj=None):
        if hasattr(request.user, 'userprofile'):
            if request.user.userprofile.role == 'admin':
                return True
            elif obj and request.user.userprofile.role in ['leader', 'employee']:
                # 组长和员工只能修改自己负责的客户
                if request.user.userprofile.role == 'leader':
                    employee_ids = UserProfile.objects.filter(
                        leader_id=request.user.id
                    ).values_list('user_id', flat=True)
                    return obj.follower_id in list(employee_ids) + [request.user.id]
                else:
                    return obj.follower_id == request.user.id
        return False

    def assign_customers(self, request, queryset):
        # 这里可以添加批量分配客户的逻辑
        self.message_user(request, f"选择了 {queryset.count()} 个客户进行分配")
    assign_customers.short_description = "分配选中的客户"

    def remove_to_pool(self, request, queryset):
        # 这里可以添加移除到公海的逻辑
        self.message_user(request, f"选择了 {queryset.count()} 个客户移除到公海")
    remove_to_pool.short_description = "移除到公海"


@admin.register(CustomerHistory)
class CustomerHistoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'phone', 'employee_id', 'follower_name', 'operation_type', 'start_date', 'end_date_history', 'snapshot_date']
    list_filter = ['operation_type', 'status', 'start_date', 'snapshot_date']
    search_fields = ['name', 'phone']
    readonly_fields = ['snapshot_date']

    fieldsets = (
        ('客户信息', {
            'fields': ('customer_id', 'name', 'phone', 'age', 'address')
        }),
        ('业务状态', {
            'fields': ('status', 'amount', ('follower_id', 'follower_name'), ('assigner_id', 'assigner_name'), ('remover_id', 'remover_name'))
        }),
        ('时间信息', {
            'fields': ('import_date', 'receive_date', 'end_date')
        }),
        ('历史记录', {
            'fields': ('employee_id', 'start_date', 'end_date_history', 'operation_type', 'operator_id', 'snapshot_date')
        }),
        ('跟进记录', {
            'fields': ('follow_records',),
            'classes': ('wide',)
        }),
    )

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if hasattr(request.user, 'userprofile'):
            if request.user.userprofile.role == 'employee':
                # 员工只能看到自己的历史记录
                return qs.filter(employee_id=request.user.id)
            elif request.user.userprofile.role == 'leader':
                # 组长看到自己和下属员工的历史记录
                employee_ids = UserProfile.objects.filter(
                    leader_id=request.user.id
                ).values_list('user_id', flat=True)
                return qs.filter(employee_id__in=list(employee_ids) + [request.user.id])
        return qs

    def has_add_permission(self, request):
        # 历史记录不允许手动添加
        return False

    def has_change_permission(self, request, obj=None):
        # 历史记录不允许修改
        return False

    def has_delete_permission(self, request, obj=None):
        # 只有主账号可以删除历史记录
        if hasattr(request.user, 'userprofile'):
            return request.user.userprofile.role == 'admin'
        return False


@admin.register(OrderRecord)
class OrderRecordAdmin(admin.ModelAdmin):
    list_display = ['customer_id', 'employee_id', 'amount', 'loan_date', 'created_at']
    list_filter = ['loan_date', 'created_at']
    search_fields = ['customer_id']
    readonly_fields = ['created_at']

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if hasattr(request.user, 'userprofile'):
            if request.user.userprofile.role == 'employee':
                # 员工只能看到自己的开单记录
                return qs.filter(employee_id=request.user.id)
            elif request.user.userprofile.role == 'leader':
                # 组长看到自己和下属员工的开单记录
                employee_ids = UserProfile.objects.filter(
                    leader_id=request.user.id
                ).values_list('user_id', flat=True)
                return qs.filter(employee_id__in=list(employee_ids) + [request.user.id])
        return qs

    def has_change_permission(self, request, obj=None):
        # 开单记录一般不允许修改，只有主账号可以
        if hasattr(request.user, 'userprofile'):
            return request.user.userprofile.role == 'admin'
        return False


# 自定义admin站点标题
admin.site.site_header = '获客管理系统'
admin.site.site_title = '获客管理系统'
admin.site.index_title = '欢迎使用获客管理系统'
