#!/usr/bin/env python
"""
更新现有客户数据的姓名字段
为已有的客户数据填充跟进人、分配人、移除人的姓名
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shecrm.settings')
django.setup()

from django.contrib.auth.models import User
from app01.models import Customer, CustomerHistory

def get_user_name(user_id):
    """根据用户ID获取用户姓名"""
    if user_id:
        try:
            user = User.objects.get(id=user_id)
            # 优先使用 first_name + last_name，如果没有则使用username
            if user.first_name or user.last_name:
                return f"{user.last_name}{user.first_name}".strip()
            else:
                return user.username
        except User.DoesNotExist:
            return f"用户ID:{user_id}"
    return ""

def update_customer_names():
    """更新客户表的姓名字段"""
    print("开始更新客户表的姓名字段...")
    
    customers = Customer.objects.all()
    updated_count = 0
    
    for customer in customers:
        updated = False
        
        # 更新跟进人姓名
        if customer.follower_id and not customer.follower_name:
            customer.follower_name = get_user_name(customer.follower_id)
            updated = True
        
        # 更新分配人姓名
        if customer.assigner_id and not customer.assigner_name:
            customer.assigner_name = get_user_name(customer.assigner_id)
            updated = True
        
        # 更新移除人姓名
        if customer.remover_id and not customer.remover_name:
            customer.remover_name = get_user_name(customer.remover_id)
            updated = True
        
        if updated:
            customer.save()
            updated_count += 1
    
    print(f"客户表：更新了 {updated_count} 条记录")

def update_customer_history_names():
    """更新客户历史表的姓名字段"""
    print("开始更新客户历史表的姓名字段...")
    
    histories = CustomerHistory.objects.all()
    updated_count = 0
    
    for history in histories:
        updated = False
        
        # 更新跟进人姓名
        if history.follower_id and not history.follower_name:
            history.follower_name = get_user_name(history.follower_id)
            updated = True
        
        # 更新分配人姓名
        if history.assigner_id and not history.assigner_name:
            history.assigner_name = get_user_name(history.assigner_id)
            updated = True
        
        # 更新移除人姓名
        if history.remover_id and not history.remover_name:
            history.remover_name = get_user_name(history.remover_id)
            updated = True
        
        if updated:
            history.save()
            updated_count += 1
    
    print(f"客户历史表：更新了 {updated_count} 条记录")

def main():
    print("开始更新姓名字段...")
    update_customer_names()
    update_customer_history_names()
    print("姓名字段更新完成！")

if __name__ == '__main__':
    main()
