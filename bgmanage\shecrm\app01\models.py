from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone

# Create your models here.

class UserProfile(models.Model):
    """用户扩展表"""
    ROLE_CHOICES = [
        ('admin', '主账号'),
        ('leader', '组长账号'),
        ('employee', '员工账号'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户')
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, verbose_name='角色')
    leader_id = models.IntegerField(null=True, blank=True, verbose_name='所属组长ID')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    is_active = models.BooleanField(default=True, verbose_name='是否激活')

    class Meta:
        verbose_name = '用户资料'
        verbose_name_plural = '用户资料'

    def __str__(self):
        return f"{self.user.username} - {self.get_role_display()}"


class Customer(models.Model):
    """客户数据表"""
    STATUS_CHOICES = [
        ('not_connected', '未接通'),
        ('inviting', '邀约中'),
        ('interested', '意向客户'),
        ('to_sign', '待签约'),
        ('loan_success', '放款成功'),
    ]

    name = models.CharField(max_length=100, verbose_name='姓名')
    phone = models.CharField(max_length=20, verbose_name='电话')
    age = models.IntegerField(verbose_name='年龄')
    address = models.TextField(verbose_name='地址')
    import_date = models.DateTimeField(verbose_name='导入日期')
    receive_date = models.DateTimeField(null=True, blank=True, verbose_name='领取日期')
    end_date = models.DateTimeField(null=True, blank=True, verbose_name='结束日期')
    follower_id = models.IntegerField(null=True, blank=True, verbose_name='跟进人ID')
    assigner_id = models.IntegerField(null=True, blank=True, verbose_name='分配人ID')
    remover_id = models.IntegerField(null=True, blank=True, verbose_name='移除人ID')
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='金额')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='not_connected', verbose_name='状态')
    follow_records = models.TextField(blank=True, verbose_name='跟进记录')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '客户数据'
        verbose_name_plural = '客户数据'
        indexes = [
            models.Index(fields=['follower_id']),
            models.Index(fields=['status']),
            models.Index(fields=['import_date']),
        ]

    def __str__(self):
        return f"{self.name} - {self.phone}"


class CustomerHistory(models.Model):
    """客户数据历史表"""
    STATUS_CHOICES = [
        ('not_connected', '未接通'),
        ('inviting', '邀约中'),
        ('interested', '意向客户'),
        ('to_sign', '待签约'),
        ('loan_success', '放款成功'),
    ]

    # 客户基本信息（备份时的状态）
    customer_id = models.IntegerField(verbose_name='客户ID')
    name = models.CharField(max_length=100, verbose_name='姓名')
    phone = models.CharField(max_length=20, verbose_name='电话')
    age = models.IntegerField(verbose_name='年龄')
    address = models.TextField(verbose_name='地址')

    # 客户业务信息（备份时的状态）
    import_date = models.DateTimeField(verbose_name='导入日期')
    receive_date = models.DateTimeField(null=True, blank=True, verbose_name='领取日期')
    end_date = models.DateTimeField(null=True, blank=True, verbose_name='结束日期')
    follower_id = models.IntegerField(null=True, blank=True, verbose_name='跟进人ID')
    assigner_id = models.IntegerField(null=True, blank=True, verbose_name='分配人ID')
    remover_id = models.IntegerField(null=True, blank=True, verbose_name='移除人ID')
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='金额')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, verbose_name='状态')
    follow_records = models.TextField(blank=True, verbose_name='跟进记录')

    # 历史记录特有字段
    employee_id = models.IntegerField(verbose_name='拥有该客户的员工ID')
    start_date = models.DateTimeField(verbose_name='开始拥有时间')
    end_date_history = models.DateTimeField(null=True, blank=True, verbose_name='结束拥有时间')
    operation_type = models.CharField(max_length=20, verbose_name='操作类型')  # assign/remove/transfer
    operator_id = models.IntegerField(verbose_name='操作人ID')
    snapshot_date = models.DateTimeField(auto_now_add=True, verbose_name='快照创建时间')

    class Meta:
        verbose_name = '客户历史记录'
        verbose_name_plural = '客户历史记录'
        indexes = [
            models.Index(fields=['customer_id', 'employee_id']),
            models.Index(fields=['start_date', 'end_date_history']),
            models.Index(fields=['operation_type']),
        ]

    def __str__(self):
        return f"{self.name} - 员工ID:{self.employee_id} - {self.operation_type}"


class OrderRecord(models.Model):
    """开单记录表"""
    customer_id = models.IntegerField(verbose_name='客户ID')
    employee_id = models.IntegerField(verbose_name='员工ID')
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='放款金额')
    loan_date = models.DateTimeField(verbose_name='放款日期')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='记录创建时间')

    class Meta:
        verbose_name = '开单记录'
        verbose_name_plural = '开单记录'
        indexes = [
            models.Index(fields=['employee_id']),
            models.Index(fields=['loan_date']),
        ]

    def __str__(self):
        return f"客户ID:{self.customer_id} - 员工ID:{self.employee_id} - {self.amount}元"
