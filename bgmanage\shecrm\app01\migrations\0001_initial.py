# Generated by Django 4.2.23 on 2025-07-02 06:15

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('admin', '主账号'), ('leader', '组长账号'), ('employee', '员工账号')], max_length=20, verbose_name='角色')),
                ('leader_id', models.IntegerField(blank=True, null=True, verbose_name='所属组长ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户资料',
                'verbose_name_plural': '用户资料',
            },
        ),
        migrations.CreateModel(
            name='OrderRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer_id', models.IntegerField(verbose_name='客户ID')),
                ('employee_id', models.IntegerField(verbose_name='员工ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='放款金额')),
                ('loan_date', models.DateTimeField(verbose_name='放款日期')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='记录创建时间')),
            ],
            options={
                'verbose_name': '开单记录',
                'verbose_name_plural': '开单记录',
                'indexes': [models.Index(fields=['employee_id'], name='app01_order_employe_86520b_idx'), models.Index(fields=['loan_date'], name='app01_order_loan_da_5d46bd_idx')],
            },
        ),
        migrations.CreateModel(
            name='CustomerHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer_id', models.IntegerField(verbose_name='客户ID')),
                ('name', models.CharField(max_length=100, verbose_name='姓名')),
                ('phone', models.CharField(max_length=20, verbose_name='电话')),
                ('age', models.IntegerField(verbose_name='年龄')),
                ('address', models.TextField(verbose_name='地址')),
                ('import_date', models.DateTimeField(verbose_name='导入日期')),
                ('receive_date', models.DateTimeField(blank=True, null=True, verbose_name='领取日期')),
                ('end_date', models.DateTimeField(blank=True, null=True, verbose_name='结束日期')),
                ('follower_id', models.IntegerField(blank=True, null=True, verbose_name='跟进人ID')),
                ('assigner_id', models.IntegerField(blank=True, null=True, verbose_name='分配人ID')),
                ('remover_id', models.IntegerField(blank=True, null=True, verbose_name='移除人ID')),
                ('amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='金额')),
                ('status', models.CharField(choices=[('not_connected', '未接通'), ('inviting', '邀约中'), ('interested', '意向客户'), ('to_sign', '待签约'), ('loan_success', '放款成功')], max_length=20, verbose_name='状态')),
                ('follow_records', models.TextField(blank=True, verbose_name='跟进记录')),
                ('employee_id', models.IntegerField(verbose_name='拥有该客户的员工ID')),
                ('start_date', models.DateTimeField(verbose_name='开始拥有时间')),
                ('end_date_history', models.DateTimeField(blank=True, null=True, verbose_name='结束拥有时间')),
                ('operation_type', models.CharField(max_length=20, verbose_name='操作类型')),
                ('operator_id', models.IntegerField(verbose_name='操作人ID')),
                ('snapshot_date', models.DateTimeField(auto_now_add=True, verbose_name='快照创建时间')),
            ],
            options={
                'verbose_name': '客户历史记录',
                'verbose_name_plural': '客户历史记录',
                'indexes': [models.Index(fields=['customer_id', 'employee_id'], name='app01_custo_custome_442b09_idx'), models.Index(fields=['start_date', 'end_date_history'], name='app01_custo_start_d_a0ca92_idx'), models.Index(fields=['operation_type'], name='app01_custo_operati_a9f03d_idx')],
            },
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='姓名')),
                ('phone', models.CharField(max_length=20, verbose_name='电话')),
                ('age', models.IntegerField(verbose_name='年龄')),
                ('address', models.TextField(verbose_name='地址')),
                ('import_date', models.DateTimeField(verbose_name='导入日期')),
                ('receive_date', models.DateTimeField(blank=True, null=True, verbose_name='领取日期')),
                ('end_date', models.DateTimeField(blank=True, null=True, verbose_name='结束日期')),
                ('follower_id', models.IntegerField(blank=True, null=True, verbose_name='跟进人ID')),
                ('assigner_id', models.IntegerField(blank=True, null=True, verbose_name='分配人ID')),
                ('remover_id', models.IntegerField(blank=True, null=True, verbose_name='移除人ID')),
                ('amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='金额')),
                ('status', models.CharField(choices=[('not_connected', '未接通'), ('inviting', '邀约中'), ('interested', '意向客户'), ('to_sign', '待签约'), ('loan_success', '放款成功')], default='not_connected', max_length=20, verbose_name='状态')),
                ('follow_records', models.TextField(blank=True, verbose_name='跟进记录')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '客户数据',
                'verbose_name_plural': '客户数据',
                'indexes': [models.Index(fields=['follower_id'], name='app01_custo_followe_b199ad_idx'), models.Index(fields=['status'], name='app01_custo_status_1aba02_idx'), models.Index(fields=['import_date'], name='app01_custo_import__33eb14_idx')],
            },
        ),
    ]
